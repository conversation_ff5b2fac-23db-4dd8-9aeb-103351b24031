import 'package:flutter/material.dart';
import 'page_flip_types.dart';

/// 页面容器 - 用于复用页面Widget，避免重复创建
class PageContainer extends StatefulWidget {
  final int containerId; // 容器ID (0, 1, 2)
  final PageBuilder pageBuilder;
  final int? currentPageIndex; // 当前显示的页面索引
  final VoidCallback? onPageBuilt; // 页面构建完成回调
  final PageContainerManager? containerManager; // 容器管理器引用

  const PageContainer({
    super.key,
    required this.containerId,
    required this.pageBuilder,
    this.currentPageIndex,
    this.onPageBuilt,
    this.containerManager,
  });

  @override
  State<PageContainer> createState() => _PageContainerState();
}

/// 页面容器控制器 - 用于外部控制页面容器的内容更新
class PageContainerController {
  _PageContainerState? _state;

  void _attach(_PageContainerState state) {
    _state = state;
  }

  void _detach() {
    _state = null;
  }

  /// 更新页面索引，触发页面重新构建
  void updatePageIndex(int? newPageIndex) {
    _state?._updatePageIndex(newPageIndex);
  }

  /// 获取当前页面索引
  int? get currentPageIndex => _state?._cachedPageIndex;

  /// 检查是否已附加到状态
  bool get isAttached => _state != null;
}

class _PageContainerState extends State<PageContainer> {
  Widget? _cachedPage;
  int? _cachedPageIndex;

  // 控制器引用，用于外部控制
  PageContainerController? _controller;

  @override
  void initState() {
    super.initState();

    // 如果有容器管理器，自动关联控制器
    if (widget.containerManager != null) {
      final controller =
          widget.containerManager!.getContainerController(widget.containerId);
      setController(controller);
    }

    _buildPageIfNeeded();
  }

  @override
  void didUpdateWidget(PageContainer oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 如果页面索引发生变化，需要重新构建页面
    if (oldWidget.currentPageIndex != widget.currentPageIndex) {
      _buildPageIfNeeded();
    }
  }

  /// 设置控制器（用于外部控制）
  void setController(PageContainerController controller) {
    _controller?._detach();
    _controller = controller;
    _controller!._attach(this);
  }

  /// 移除控制器
  void removeController() {
    _controller?._detach();
    _controller = null;
  }

  /// 外部调用的页面索引更新方法
  void _updatePageIndex(int? newPageIndex) {
    if (newPageIndex != _cachedPageIndex) {
      if (mounted) {
        setState(() {
          _buildPageWithIndex(newPageIndex);
        });
      } else {
        _buildPageWithIndex(newPageIndex);
      }
    }
  }

  @override
  void dispose() {
    removeController();
    super.dispose();
  }

  void _buildPageIfNeeded() {
    _buildPageWithIndex(widget.currentPageIndex);
  }

  /// 使用指定的页面索引构建页面
  void _buildPageWithIndex(int? pageIndex) {
    if (pageIndex != null && pageIndex != _cachedPageIndex) {
      try {
        _cachedPage = widget.pageBuilder(pageIndex);
        _cachedPageIndex = pageIndex;

        // 通知页面构建完成
        WidgetsBinding.instance.addPostFrameCallback((_) {
          widget.onPageBuilt?.call();
        });
      } catch (e) {
        // 构建失败时显示错误页面
        _cachedPage = _buildErrorPage(pageIndex, e);
        _cachedPageIndex = pageIndex;
      }
    } else if (pageIndex == null && _cachedPageIndex != null) {
      // 清空页面内容
      _cachedPage = null;
      _cachedPageIndex = null;
    }
  }

  Widget _buildErrorPage(int index, dynamic error) {
    return Container(
      color: Colors.red[100],
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              '页面 $index 加载失败',
              style: const TextStyle(fontSize: 18, color: Colors.red),
            ),
            const SizedBox(height: 8),
            Text(
              error.toString(),
              style: const TextStyle(fontSize: 12, color: Colors.red),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('🔍 容器${widget.containerId}实际显示: 页面$_cachedPageIndex');

    // 如果没有缓存页面，显示空白
    if (_cachedPage == null) {
      return Container(
        color: Colors.transparent,
        child: const SizedBox.expand(),
      );
    }

    return _cachedPage!;
  }
}

/// 页面容器管理器 - 管理3个页面容器的状态和数据
class PageContainerManager {
  static const int containerCount = 3;

  // 3个容器的页面索引映射
  final List<int?> _containerPageIndexes = List.filled(containerCount, null);

  // 当前页面索引
  int _currentPageIndex = 0;

  // 页面总数
  final int pageCount;

  // 页面容器控制器列表
  final List<PageContainerController> _containerControllers =
      List.generate(containerCount, (index) => PageContainerController());

  PageContainerManager({required this.pageCount}) {
    // 初始化时更新容器映射
    _updateContainerMapping();
  }

  /// 获取当前页面索引
  int get currentPageIndex => _currentPageIndex;

  /// 获取指定容器的控制器
  PageContainerController getContainerController(int containerId) {
    if (containerId < 0 || containerId >= containerCount) {
      throw ArgumentError('Container ID $containerId out of range');
    }
    return _containerControllers[containerId];
  }

  /// 设置当前页面索引并更新容器映射
  void setCurrentPageIndex(int index) {
    if (index < 0 || index >= pageCount) return;

    final oldIndex = _currentPageIndex;
    _currentPageIndex = index;
    _updateContainerMapping();

    // 如果页面索引发生变化，通知所有容器控制器更新
    if (oldIndex != _currentPageIndex) {
      _notifyControllersUpdate();
    }
  }

  /// 更新容器映射关系
  void _updateContainerMapping() {
    // 容器0：前一页 (currentIndex - 1)
    // 容器1：当前页 (currentIndex)
    // 容器2：下一页 (currentIndex + 1)

    _containerPageIndexes[0] =
        _currentPageIndex > 0 ? _currentPageIndex - 1 : null;
    _containerPageIndexes[1] = _currentPageIndex;
    _containerPageIndexes[2] =
        _currentPageIndex < pageCount - 1 ? _currentPageIndex + 1 : null;
  }

  /// 通知所有容器控制器更新页面内容
  void _notifyControllersUpdate() {
    for (int i = 0; i < containerCount; i++) {
      final controller = _containerControllers[i];
      final newPageIndex = _containerPageIndexes[i];

      // 只有当控制器已附加到状态时才更新
      if (controller.isAttached) {
        controller.updatePageIndex(newPageIndex);
      }
    }
  }

  /// 获取指定容器应该显示的页面索引
  int? getContainerPageIndex(int containerId) {
    if (containerId < 0 || containerId >= containerCount) return null;
    return _containerPageIndexes[containerId];
  }

  /// 获取指定页面索引对应的容器ID
  int? getContainerIdForPage(int pageIndex) {
    for (int i = 0; i < containerCount; i++) {
      if (_containerPageIndexes[i] == pageIndex) {
        return i;
      }
    }
    return null;
  }

  /// 获取所有容器的页面索引映射
  List<int?> getAllContainerPageIndexes() {
    return List.from(_containerPageIndexes);
  }

  /// 重置所有容器
  void reset() {
    _containerPageIndexes.fillRange(0, containerCount, null);
    _currentPageIndex = 0;
  }
}
