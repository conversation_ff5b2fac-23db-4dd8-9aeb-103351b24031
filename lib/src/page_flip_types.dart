import 'package:flutter/widgets.dart';

/// 翻页动画类型
enum PageFlipAnimation {
  /// 卷曲效果
  curl,

  /// 覆盖效果
  cover,

  /// 3D翻转效果
  flip,

  /// 仿真效果
  simulation,

  /// 仿真效果2
  simulation2,
}

/// 交互模式
enum PageFlipInteractionMode {
  /// 自动模式：简单动画保持交互性，复杂动画转换为图片
  auto,

  /// 交互模式：始终保持Widget交互性，使用简化动画
  interactive,

  /// 图片模式：始终转换为图片，支持所有复杂动画
  picture,
}

/// 页面构建器类型
typedef PageBuilder = Widget Function(int index);

/// 页面切换回调
typedef PageChangeCallback = void Function(int index);

/// 预加载完成回调
typedef PreloadCallback = void Function(int index);

/// 内存清理回调
typedef MemoryCleanupCallback = void Function(List<int> clearedIndexes);

/// 页面缓存信息
class PageCacheInfo {
  final int index;
  final DateTime lastAccessed;
  final bool isPreloaded;

  const PageCacheInfo({
    required this.index,
    required this.lastAccessed,
    required this.isPreloaded,
  });
}

/// 翻页方向
enum FlipDirection {
  forward,
  backward,
}
