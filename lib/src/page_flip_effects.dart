import 'dart:math' as math;
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'page_flip_types.dart';
import 'page_flip_config.dart';
import 'page_flip_controller.dart';

/// 翻页效果绘制器
class PageFlipEffectPainter extends CustomPainter {
  final Animation<double> animation;
  final int pageIndex;
  final PageFlipController controller;
  final PageFlipConfig config;
  final ui.Picture? picture;
  final ui.Picture? nextPicture;
  final double radius;

  PageFlipEffectPainter({
    required this.animation,
    required this.pageIndex,
    required this.controller,
    required this.config,
    this.picture,
    this.nextPicture,
    this.radius = 0.18,
  }) : super(repaint: animation);

  @override
  void paint(Canvas canvas, Size size) {
    // 检查是否需要绘制
    if (pageIndex > controller.currentIndex + 2 ||
        pageIndex < controller.currentIndex - 2) {
      return;
    }

    // 检查上一页动画位置，优化渲染
    if (controller.getAnimationPosition(pageIndex - 1) > 0.998) {
      return;
    }

    // 异步获取picture - 这里需要特殊处理
    // 在实际使用中，需要通过Future或回调机制来获取picture
    if (picture == null) {
      // 先绘制占位内容
      _paintPlaceholder(canvas, size);
      return;
    }

    // 状态栏裁剪 - 简化实现，实际使用中应从上下文获取
    if (config.showStatus && !config.animationStatus) {
      // 假设状态栏高度约44像素（iOS）
      const statusBarHeight = 44.0;
      canvas
          .clipRect(Rect.fromLTRB(0, statusBarHeight, size.width, size.height));
    }

    final pos = animation.value;

    // 完全显示
    if (pos > 0.998) {
      canvas.drawPicture(picture!);
      if (config.animation == PageFlipAnimation.simulation2) {
        _drawMiddleShadow(canvas, size);
      }
      return;
    }

    // 完全隐藏
    if (pos < 0.002) {
      return;
    }

    // 根据动画类型绘制
    switch (config.animation) {
      case PageFlipAnimation.curl:
        _paintCurl(canvas, size, pos, picture!);
        break;
      case PageFlipAnimation.cover:
        _paintCover(canvas, size, pos, picture!);
        break;
      case PageFlipAnimation.flip:
        _paintFlip(canvas, size, pos, picture!, nextPicture);
        break;
      case PageFlipAnimation.simulation:
        _paintSimulation(canvas, size, pos, picture!);
        break;
      case PageFlipAnimation.simulation2:
        _paintSimulation2(canvas, size, pos, picture!);
        break;
    }
  }

  /// 卷曲效果
  void _paintCurl(Canvas canvas, Size size, double pos, ui.Picture picture) {
    final movX = (1.0 - pos) * 0.85;
    final calcR = (movX < 0.20) ? radius * movX * 5 : radius;
    final wHRatio = 1 - calcR;
    final hWRatio = size.height / size.width;
    final hWCorrection = (hWRatio - 1.0) / 2.0;

    final w = size.width;
    final h = size.height;
    final shadowXf = (wHRatio - movX);
    final shadowSigma =
        Shadow.convertRadiusToSigma(8.0 + (32.0 * (1.0 - shadowXf)));
    final pageRect = Rect.fromLTRB(0.0, 0.0, w * shadowXf, h);

    // 绘制背景
    canvas.drawRect(pageRect, Paint()..color = config.backgroundColor);

    // 绘制阴影
    if (pos != 0) {
      canvas.drawRect(
        pageRect,
        Paint()
          ..color = Colors.black54
          ..maskFilter = MaskFilter.blur(BlurStyle.outer, shadowSigma),
      );
    }

    // 卷曲效果绘制
    final recorder = ui.PictureRecorder();
    final tempCanvas = Canvas(recorder);
    tempCanvas.drawPicture(picture);
    final tempPicture = recorder.endRecording();

    for (double x = 0; x < w; x++) {
      final xf = (x / w);
      final v = (calcR * (math.sin(math.pi / 0.5 * (xf - (1.0 - pos)))) +
          (calcR * 1.1));
      final xv = (xf * wHRatio) - movX;
      if (xv < 0) continue;

      final yv = ((h * calcR * movX) * hWRatio) - hWCorrection;
      final ds = (yv * v);
      final dr = Rect.fromLTRB(xv * w, 0.0 - ds, xv * w + 1.0, h + ds);

      canvas.save();
      canvas.clipRect(dr);
      canvas.translate(xv * w, -ds);
      canvas.drawPicture(tempPicture);
      canvas.restore();
    }
  }

  /// 覆盖效果
  void _paintCover(Canvas canvas, Size size, double pos, ui.Picture picture) {
    final right = pos * size.width;
    final shadowSigma = Shadow.convertRadiusToSigma(16);
    final pageRect = Rect.fromLTRB(0.0, 0.0, right, size.height);

    // 绘制阴影
    canvas.drawRect(
      pageRect,
      Paint()
        ..color = Colors.black54
        ..maskFilter = MaskFilter.blur(BlurStyle.outer, shadowSigma),
    );

    // 平移并绘制picture
    canvas.save();
    canvas.translate(right - size.width, 0);
    canvas.drawPicture(picture);
    canvas.restore();
  }

  /// 3D翻转效果
  void _paintFlip(Canvas canvas, Size size, double pos, ui.Picture picture,
      ui.Picture? nextPicture) {
    if (pos > 0.5) {
      // 后半段动画
      canvas.drawPicture(picture);
      canvas
          .clipRect(Rect.fromLTRB(size.width / 2, 0, size.width, size.height));

      if (nextPicture != null) {
        canvas.drawPicture(nextPicture);
      }

      canvas.save();
      final transform = Matrix4.identity()
        ..setEntry(3, 2, 0.0005)
        ..translate(size.width / 2, 0, 0)
        ..rotateY(math.pi * (1 - pos))
        ..translate(-size.width / 2, 0, 0);
      canvas.transform(transform.storage);

      canvas.drawRect(
        Offset.zero & size,
        Paint()
          ..color = Colors.black54
          ..maskFilter = MaskFilter.blur(BlurStyle.outer, 20),
      );
      canvas.drawPicture(picture);
      canvas.restore();
    } else {
      // 前半段动画
      if (nextPicture != null) {
        canvas.drawPicture(nextPicture);
      }

      canvas.clipRect(Rect.fromLTRB(0, 0, size.width / 2, size.height));
      canvas.drawPicture(picture);

      canvas.save();
      final transform2 = Matrix4.identity()
        ..setEntry(3, 2, 0.0005)
        ..translate(size.width / 2, 0, 0)
        ..rotateY(-math.pi * pos)
        ..translate(-size.width / 2, 0, 0);
      canvas.transform(transform2.storage);

      canvas.drawRect(
        Offset.zero & size,
        Paint()
          ..color = Colors.black54
          ..maskFilter = MaskFilter.blur(BlurStyle.outer, 20),
      );

      if (nextPicture != null) {
        canvas.drawPicture(nextPicture);
      }
      canvas.restore();
    }
  }

  /// 仿真效果
  void _paintSimulation(
      Canvas canvas, Size size, double pos, ui.Picture picture) {
    final w = size.width;
    final h = size.height;
    final right = pos * w;
    final left = 2 * right - w;

    // 左侧部分
    canvas.save();
    canvas.clipRect(Rect.fromLTRB(0, 0, left, h));
    canvas.drawPicture(picture);
    canvas.restore();

    // 左侧阴影
    if (left > 0) {
      canvas.drawRect(
        Rect.fromLTRB(left - 5, 0, left, h),
        Paint()
          ..color = Colors.black26
          ..maskFilter = MaskFilter.blur(BlurStyle.normal, 5),
      );
    }

    // 中间翻页部分
    canvas.save();
    canvas.clipRect(Rect.fromLTRB(left, 0, right, h));
    canvas.translate(right - w, 0);
    canvas.drawPicture(picture);
    canvas.restore();

    // 右侧阴影
    canvas.drawRect(
      Rect.fromLTRB(right, 0, right + 5, h),
      Paint()
        ..color = Colors.black26
        ..maskFilter = MaskFilter.blur(BlurStyle.normal, 5),
    );
  }

  /// 仿真效果2
  void _paintSimulation2(
      Canvas canvas, Size size, double pos, ui.Picture picture) {
    _paintSimulation(canvas, size, pos, picture);
    _drawMiddleShadow(canvas, size);
  }

  /// 绘制中间阴影
  void _drawMiddleShadow(Canvas canvas, Size size) {
    final centerX = size.width / 2;
    final shadowWidth = 4.0;

    canvas.drawRect(
      Rect.fromLTRB(
          centerX - shadowWidth / 2, 0, centerX + shadowWidth / 2, size.height),
      Paint()
        ..color = Colors.black12
        ..maskFilter = MaskFilter.blur(BlurStyle.normal, 2),
    );
  }

  /// 绘制占位符
  void _paintPlaceholder(Canvas canvas, Size size) {
    // 绘制背景
    canvas.drawRect(
      Offset.zero & size,
      Paint()..color = config.backgroundColor,
    );

    // 绘制加载提示
    final textPainter = TextPainter(
      text: TextSpan(
        text: 'Loading page $pageIndex...',
        style: const TextStyle(
          color: Colors.grey,
          fontSize: 16,
        ),
      ),
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();
    final offset = Offset(
      (size.width - textPainter.width) / 2,
      (size.height - textPainter.height) / 2,
    );
    textPainter.paint(canvas, offset);
  }

  @override
  bool shouldRepaint(PageFlipEffectPainter oldDelegate) {
    return oldDelegate.animation.value != animation.value ||
        oldDelegate.pageIndex != pageIndex ||
        oldDelegate.picture != picture ||
        oldDelegate.nextPicture != nextPicture;
  }
}
