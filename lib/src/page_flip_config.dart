import 'package:flutter/material.dart';
import 'page_flip_types.dart';

/// 翻页组件配置
class PageFlipConfig {
  /// 动画类型
  final PageFlipAnimation animation;

  /// 动画持续时间（毫秒）
  final int animationDuration;

  /// 背景颜色
  final Color backgroundColor;

  /// 是否启用手势控制
  final bool enableGestures;

  /// 是否启用键盘控制
  final bool enableKeyboard;

  /// 单手模式（反转左右点击行为）
  final bool oneHandMode;

  /// 菜单区域比例
  final double menuZoneRatio;

  /// 是否显示状态栏
  final bool showStatus;

  /// 动画时是否显示状态栏
  final bool animationStatus;

  /// 交互模式
  final PageFlipInteractionMode interactionMode;

  /// 是否启用点击切换页面
  final bool enableTapToFlip;

  /// 翻页速度阈值（像素/秒）
  /// 当滑动速度超过此值时，无论滑动距离多少都会触发翻页
  /// 默认值600，降低此值可以让用户更容易通过快速滑动触发翻页
  final double flipVelocityThreshold;

  /// 翻页距离阈值（0.0-1.0）
  /// 当滑动距离超过页面宽度的此比例时会触发翻页
  /// 默认值0.35（35%），降低此值可以让用户滑动更短距离就能翻页
  final double flipDistanceThreshold;

  const PageFlipConfig({
    this.animation = PageFlipAnimation.cover,
    this.animationDuration = 300,
    this.backgroundColor = Colors.white,
    this.enableGestures = true,
    this.enableKeyboard = true,
    this.oneHandMode = false,
    this.menuZoneRatio = 0.33,
    this.showStatus = true,
    this.animationStatus = false,
    this.interactionMode = PageFlipInteractionMode.auto,
    this.enableTapToFlip = true,
    this.flipVelocityThreshold = 600.0,
    this.flipDistanceThreshold = 0.35,
  });

  /// 复制配置并更新指定参数
  PageFlipConfig copyWith({
    PageFlipAnimation? animation,
    int? animationDuration,
    Color? backgroundColor,
    bool? enableGestures,
    bool? enableKeyboard,
    bool? oneHandMode,
    double? menuZoneRatio,
    bool? showStatus,
    bool? animationStatus,
    PageFlipInteractionMode? interactionMode,
    bool? enableTapToFlip,
    double? flipVelocityThreshold,
    double? flipDistanceThreshold,
  }) {
    return PageFlipConfig(
      animation: animation ?? this.animation,
      animationDuration: animationDuration ?? this.animationDuration,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      enableGestures: enableGestures ?? this.enableGestures,
      enableKeyboard: enableKeyboard ?? this.enableKeyboard,
      oneHandMode: oneHandMode ?? this.oneHandMode,
      menuZoneRatio: menuZoneRatio ?? this.menuZoneRatio,
      showStatus: showStatus ?? this.showStatus,
      animationStatus: animationStatus ?? this.animationStatus,
      interactionMode: interactionMode ?? this.interactionMode,
      enableTapToFlip: enableTapToFlip ?? this.enableTapToFlip,
      flipVelocityThreshold:
          flipVelocityThreshold ?? this.flipVelocityThreshold,
      flipDistanceThreshold:
          flipDistanceThreshold ?? this.flipDistanceThreshold,
    );
  }
}
