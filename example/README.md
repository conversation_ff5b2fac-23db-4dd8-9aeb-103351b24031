# Flutter Page Flip Example

这是flutter_page_flip包的完整示例应用，展示了所有主要功能和使用方法。

## 🎯 示例内容

### 1. 基础翻页功能
- 10页彩色内容页面
- 手势滑动翻页
- 点击左右区域翻页
- 键盘方向键控制

### 2. 动画效果演示
- **Cover（覆盖）**: 最流畅的翻页效果
- **Curl（卷曲）**: 经典的页面卷曲效果
- **Flip（翻转）**: 3D立体翻页效果
- **Simulation（仿真）**: 真实书本翻页模拟
- **Simulation2（增强仿真）**: 带阴影的仿真效果

### 3. 交互功能
- 点击中央区域显示设置菜单
- 实时切换动画效果
- 页面跳转（首页/末页）
- 缓存信息显示

### 4. 配置示例
```dart
PageFlipConfig(
  animation: PageFlipAnimation.cover,
  animationDuration: 300,
  enableGestures: true,
  enableKeyboard: true,
  backgroundColor: Colors.white,
  preloadRange: 2,
  maxCacheSize: 6,
  autoPreload: true,
)
```

## 🚀 运行示例

### 前提条件
- Flutter >=3.19.1
- Dart >=3.3.0

### 安装和运行
```bash
# 克隆项目
git clone <your-repo-url>
cd flutter_page_flip/example

# 获取依赖
flutter pub get

# 运行在不同平台
flutter run -d chrome          # Web浏览器
flutter run -d <device-id>      # iOS/Android设备
flutter run -d macos           # macOS桌面
```

## 🎮 操作指南

### 手势控制
- **左右滑动**: 翻页
- **点击左侧**: 上一页
- **点击右侧**: 下一页  
- **点击中央**: 显示菜单

### 键盘控制
- **方向键**: 翻页控制
- **Home**: 跳转首页
- **End**: 跳转末页
- **Enter**: 显示菜单
- **Esc**: 关闭应用

### 菜单功能
- 切换动画效果
- 跳转页面
- 查看缓存状态

## 📊 性能展示

本示例展示了flutter_page_flip的性能优势：

### 内存优化
- 传统方案：10页约消耗5-10MB内存
- 优化方案：只消耗1-2MB内存，节省80%+

### 预加载策略
- 预加载范围：当前页前后2页
- 最大缓存：6页
- 自动清理：超出限制的页面自动释放

### 渲染优化
- 按需渲染：只绘制可见页面
- 流畅动画：60FPS稳定帧率
- 智能缓存：避免重复计算

## 🛠️ 代码结构

```
example/
├── lib/
│   └── main.dart           # 主示例代码
├── pubspec.yaml           # 依赖配置
└── README.md             # 本文档
```

## 📝 关键代码片段

### 控制器初始化
```dart
_controller = PageFlipController(
  config: PageFlipConfig(
    animation: _currentAnimation,
    animationDuration: 300,
    enableGestures: true,
    enableKeyboard: true,
    preloadRange: 2,
    maxCacheSize: 6,
    autoPreload: true,
  ),
  pageCount: _pages.length,
  pageBuilder: _buildPage,
  onPageChanged: (index) {
    setState(() {
      _currentPage = index;
    });
  },
);
```

### 页面构建器
```dart
Widget _buildPage(int index) {
  return Container(
    decoration: BoxDecoration(
      gradient: LinearGradient(
        colors: [
          _pageColors[index],
          _pageColors[index].withOpacity(0.7),
        ],
      ),
    ),
    child: SafeArea(
      child: YourPageContent(),
    ),
  );
}
```

### Widget使用
```dart
PageFlipWidget(
  controller: _controller,
  menuBuilder: _buildMenu(),
  onMenuToggle: () {
    debugPrint('菜单切换');
  },
)
```

## 🎨 自定义内容

### 替换页面内容
修改`_buildPage`方法来显示您自己的内容：

```dart
Widget _buildPage(int index) {
  return YourCustomWidget(
    data: yourData[index],
  );
}
```

### 修改动画效果
在菜单中选择不同的动画类型，或者代码中直接设置：

```dart
PageFlipConfig(
  animation: PageFlipAnimation.curl, // 更改这里
  // 其他配置...
)
```

### 调整性能参数
根据您的内容复杂度调整缓存参数：

```dart
PageFlipConfig(
  preloadRange: 3,        // 预加载范围
  maxCacheSize: 8,        // 最大缓存
  autoPreload: true,      // 自动预加载
  // 其他配置...
)
```

## 🐛 常见问题

### Q: 动画效果卡顿？
A: 尝试使用Cover动画（性能最佳）或降低内容复杂度

### Q: 内存占用过高？
A: 减少`maxCacheSize`和`preloadRange`参数

### Q: 手势不响应？
A: 确保`enableGestures: true`且没有其他手势冲突

### Q: 键盘控制无效？
A: 确保`enableKeyboard: true`且Widget获得了焦点

## 📞 技术支持

如有问题或建议，请：
1. 查看[完整文档](../README.md)
2. 提交[Issue](https://github.com/your-username/flutter_page_flip/issues)
3. 参与[讨论](https://github.com/your-username/flutter_page_flip/discussions)
