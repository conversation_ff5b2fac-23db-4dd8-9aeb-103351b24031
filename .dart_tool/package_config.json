{"configVersion": 2, "packages": [{"name": "async", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/async-2.11.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "boolean_selector", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/boolean_selector-2.1.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "characters", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/characters-1.3.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "clock", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/clock-1.1.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "collection", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/collection-1.18.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "fake_async", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/fake_async-1.3.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter", "rootUri": "file:///Users/<USER>/fvm/versions/3.19.1/packages/flutter", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "flutter_lints", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/flutter_lints-4.0.0", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "flutter_test", "rootUri": "file:///Users/<USER>/fvm/versions/3.19.1/packages/flutter_test", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/leak_tracker-10.0.0", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "leak_tracker_flutter_testing", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/leak_tracker_flutter_testing-2.0.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "leak_tracker_testing", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/leak_tracker_testing-2.0.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "lints", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/lints-4.0.0", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "matcher", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/matcher-0.12.16+1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "material_color_utilities", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/material_color_utilities-0.8.0", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "meta", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/meta-1.11.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "path", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/path-1.9.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "sky_engine", "rootUri": "file:///Users/<USER>/fvm/versions/3.19.1/bin/cache/pkg/sky_engine", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "source_span", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/source_span-1.10.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "stack_trace", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/stack_trace-1.11.1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "stream_channel", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/stream_channel-2.1.2", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "string_scanner", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/string_scanner-1.2.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "term_glyph", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/term_glyph-1.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "test_api", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/test_api-0.6.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "vector_math", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/vector_math-2.1.4", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "vm_service", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/vm_service-13.0.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "flutter_page_flip", "rootUri": "../", "packageUri": "lib/", "languageVersion": "3.3"}], "generated": "2025-06-24T16:10:01.178170Z", "generator": "pub", "generatorVersion": "3.3.0"}